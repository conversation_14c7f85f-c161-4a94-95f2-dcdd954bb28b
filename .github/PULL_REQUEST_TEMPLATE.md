Thank you for your contribution to OpenRA!

Please be aware that we do not have enough project maintainers to match the rate of contributions, so it may take several days before somebody is able to respond to your Pull Request.

You can help speed up the review process by following a few steps:

* Make sure that you have read and understand the OpenRA Coding Standard (see https://github.com/OpenRA/OpenRA/wiki/Coding-Standard).
* Write quality commit messages (see https://chris.beams.io/posts/git-commit/).
* Only commit changes that directly relate to your Pull Request.  Use your Git interface to unstage any unrelated changes to project files, line endings, whitespace, or other files.
* Review the code diff view below to double check that your changes satisfy the above three points.
* Use the `make test` and `make check` commands to check for (and fix!) any issues that are reported by our automated tests.
* If you are changing shared mod or engine code, make sure that you have tested your changes in all four default mods.
* Respond to review comments as soon as you reasonably can.  Reviewers will usually prioritize Pull Requests that are still fresh in their minds.  Make sure to leave a comment when you push new changes, otherwise GitHub does not automatically notify reviewers!
* Leave a polite comment asking for reviews if a week or more has passed without feedback.

If you need any help you can ask on Discord (https://discord.openra.net) or in the #openra IRC channel on Libera (not as active as Discord).